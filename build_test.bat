@echo off
echo ========================================
echo 航向角掉电保存功能编译测试
echo ========================================

echo.
echo 检查修改的文件...
if exist "Protocol\SetParaBao.h" (
    echo [OK] Protocol\SetParaBao.h 存在
) else (
    echo [ERROR] Protocol\SetParaBao.h 不存在
    goto :error
)

if exist "Protocol\SetParaBao.c" (
    echo [OK] Protocol\SetParaBao.c 存在
) else (
    echo [ERROR] Protocol\SetParaBao.c 不存在
    goto :error
)

if exist "NAV\nav_imu.c" (
    echo [OK] NAV\nav_imu.c 存在
) else (
    echo [ERROR] NAV\nav_imu.c 不存在
    goto :error
)

if exist "NAV\nav_app.c" (
    echo [OK] NAV\nav_app.c 存在
) else (
    echo [ERROR] NAV\nav_app.c 不存在
    goto :error
)

if exist "Test\test_azimuth_save.c" (
    echo [OK] Test\test_azimuth_save.c 存在
) else (
    echo [ERROR] Test\test_azimuth_save.c 不存在
    goto :error
)

if exist "Test\test_azimuth_save.h" (
    echo [OK] Test\test_azimuth_save.h 存在
) else (
    echo [ERROR] Test\test_azimuth_save.h 不存在
    goto :error
)

if exist "Source\src\main.c" (
    echo [OK] Source\src\main.c 存在
) else (
    echo [ERROR] Source\src\main.c 不存在
    goto :error
)

echo.
echo 检查关键函数定义...
findstr /C:"SaveAzimuthToFlash" Protocol\SetParaBao.c >nul
if %errorlevel%==0 (
    echo [OK] SaveAzimuthToFlash 函数已定义
) else (
    echo [ERROR] SaveAzimuthToFlash 函数未找到
    goto :error
)

findstr /C:"RestoreAzimuthFromFlash" Protocol\SetParaBao.c >nul
if %errorlevel%==0 (
    echo [OK] RestoreAzimuthFromFlash 函数已定义
) else (
    echo [ERROR] RestoreAzimuthFromFlash 函数未找到
    goto :error
)

findstr /C:"InitAzimuthSaveSystem" Protocol\SetParaBao.c >nul
if %errorlevel%==0 (
    echo [OK] InitAzimuthSaveSystem 函数已定义
) else (
    echo [ERROR] InitAzimuthSaveSystem 函数未找到
    goto :error
)

echo.
echo 检查结构体扩展...
findstr /C:"SavedAzimuth" Protocol\SetParaBao.h >nul
if %errorlevel%==0 (
    echo [OK] SavedAzimuth 字段已添加
) else (
    echo [ERROR] SavedAzimuth 字段未找到
    goto :error
)

findstr /C:"AzimuthValidFlag" Protocol\SetParaBao.h >nul
if %errorlevel%==0 (
    echo [OK] AzimuthValidFlag 字段已添加
) else (
    echo [ERROR] AzimuthValidFlag 字段未找到
    goto :error
)

echo.
echo 检查导航系统集成...
findstr /C:"RestoreAzimuthFromFlash" NAV\nav_imu.c >nul
if %errorlevel%==0 (
    echo [OK] 导航初始化已集成航向角恢复
) else (
    echo [ERROR] 导航初始化未集成航向角恢复
    goto :error
)

findstr /C:"SaveAzimuthToFlash" NAV\nav_app.c >nul
if %errorlevel%==0 (
    echo [OK] 导航主循环已集成航向角保存
) else (
    echo [ERROR] 导航主循环未集成航向角保存
    goto :error
)

echo.
echo ========================================
echo 所有检查通过！航向角掉电保存功能已成功集成
echo ========================================
echo.
echo 功能说明:
echo 1. 系统启动时自动恢复保存的航向角
echo 2. 导航运行时每1000次更新保存一次航向角
echo 3. 数据保存在Flash中，掉电不丢失
echo 4. 包含完整的测试程序验证功能
echo.
echo 使用方法:
echo 1. 正常编译和运行系统即可使用
echo 2. 如需测试，定义 DEBUG_AZIMUTH_SAVE_TEST 宏
echo 3. 查看 README_azimuth_save.md 获取详细说明
echo.
goto :end

:error
echo.
echo ========================================
echo 检查失败！请检查文件和代码修改
echo ========================================
exit /b 1

:end
echo 按任意键退出...
pause >nul
