# 航向角掉电保存功能说明

## 功能概述

本功能实现了航向角（azimuth）的掉电保存，确保系统重启后能够恢复之前保存的航向角值，而不是重新初始化为0。

## 功能特性

1. **自动保存**: 在导航运行状态下，每1000次导航更新自动保存一次航向角
2. **掉电保存**: 航向角数据保存在Flash中，断电后数据不丢失
3. **自动恢复**: 系统重启时自动从Flash恢复保存的航向角
4. **数据验证**: 使用有效标志确保恢复的数据是有效的
5. **保存计数**: 记录航向角保存次数，便于调试和监控

## 实现原理

### 数据结构扩展

在 `Setpara_Data` 结构体中添加了以下字段：
```c
double SavedAzimuth;        // 保存的航向角(弧度)
uint32_t AzimuthValidFlag;  // 航向角有效标志 0xA5A5A5A5表示有效
uint32_t AzimuthSaveCount;  // 航向角保存次数计数器
```

### 核心函数

1. **InitAzimuthSaveSystem()**: 初始化航向角保存系统
2. **SaveAzimuthToFlash()**: 保存航向角到Flash
3. **RestoreAzimuthFromFlash()**: 从Flash恢复航向角
4. **IsAzimuthSaveValid()**: 检查保存的航向角是否有效

### 保存时机

- 在导航运行状态（E_NAV_STATUS_IN_NAV）下
- 每1000次导航更新保存一次（约5秒间隔，假设导航频率200Hz）
- 保存当前的 `NAV_Data_Full.SINS.att[2]` 值

### 恢复时机

- 系统启动时调用 `ReadParaFromFlash()` 初始化航向角保存系统
- 在SINS初始化时，如果有有效的保存数据，则恢复航向角
- 否则使用默认值0

## 文件修改清单

### 1. Protocol/SetParaBao.h
- 扩展 `Setpara_Data` 结构体
- 添加航向角保存功能的函数声明

### 2. Protocol/SetParaBao.c
- 实现航向角保存和恢复的核心函数
- 在 `ReadParaFromFlash()` 中添加初始化调用

### 3. NAV/nav_imu.c
- 修改航向角初始化逻辑，支持从Flash恢复
- 添加 `SetParaBao.h` 头文件包含

### 4. NAV/nav_app.c
- 在导航主循环中添加定期保存逻辑
- 添加 `SetParaBao.h` 头文件包含

### 5. Test/test_azimuth_save.c (新增)
- 完整的测试程序，验证保存和恢复功能

### 6. Test/test_azimuth_save.h (新增)
- 测试程序的头文件

### 7. Source/src/main.c
- 添加测试程序的调用（可选，通过宏控制）

## 使用方法

### 正常使用

功能已集成到系统中，无需额外操作：

1. 系统启动后会自动尝试恢复保存的航向角
2. 在导航运行过程中会自动定期保存航向角
3. 系统重启后会保持之前的航向角值

### 测试验证

如需运行测试程序验证功能：

1. 在编译时定义宏 `DEBUG_AZIMUTH_SAVE_TEST`
2. 系统启动时会自动运行测试程序
3. 通过串口输出查看测试结果

### 调试信息

可以调用 `print_azimuth_debug_info()` 函数查看当前状态：
- 保存的航向角值
- 有效标志状态
- 保存次数
- 当前导航航向角

## 配置参数

### 保存频率
```c
// 在 NAV/nav_app.c 中修改保存间隔
if (azimuth_save_counter >= 1000) { // 修改此值调整保存频率
```

### 有效标志
```c
// 在 Protocol/SetParaBao.c 中定义
#define AZIMUTH_VALID_FLAG 0xA5A5A5A5
```

## 注意事项

1. **Flash寿命**: 频繁写入Flash会影响其寿命，当前设置为每5秒保存一次是合理的平衡
2. **数据一致性**: 保存操作包括擦除和写入，确保操作的原子性
3. **初始化顺序**: 确保在导航系统初始化前完成航向角保存系统的初始化
4. **角度范围**: 航向角以弧度为单位，范围通常为 [0, 2π] 或 [-π, π]

## 故障排除

### 问题1: 重启后航向角仍为0
- 检查 `IsAzimuthSaveValid()` 返回值
- 确认Flash读写操作是否成功
- 检查初始化顺序

### 问题2: 保存功能不工作
- 确认导航状态是否为 `E_NAV_STATUS_IN_NAV`
- 检查保存计数器是否递增
- 验证Flash写入权限

### 问题3: 恢复的航向角不正确
- 检查保存时的航向角值是否正确
- 确认Flash数据完整性
- 验证数据结构对齐

## 版本信息

- 版本: v1.0.0
- 创建日期: 2025-01-27
- 作者: AI Assistant
- 兼容性: INS370-8K24-INS600M-21A 系统
