Dependencies for Project 'INS', Target 'INS_4000': (DO NOT MODIFY !)
CompilerVersion: 5060960::V5.06 update 7 (build 960)::.\ARMCC
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_adc.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_adc.o --omf_browse .\objects\gd32f4xx_adc.crf --depend .\objects\gd32f4xx_adc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_can.c)(0x6507C602)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_can.o --omf_browse .\objects\gd32f4xx_can.crf --depend .\objects\gd32f4xx_can.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_crc.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_crc.o --omf_browse .\objects\gd32f4xx_crc.crf --depend .\objects\gd32f4xx_crc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ctc.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_ctc.o --omf_browse .\objects\gd32f4xx_ctc.crf --depend .\objects\gd32f4xx_ctc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dac.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_dac.o --omf_browse .\objects\gd32f4xx_dac.crf --depend .\objects\gd32f4xx_dac.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dbg.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_dbg.o --omf_browse .\objects\gd32f4xx_dbg.crf --depend .\objects\gd32f4xx_dbg.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dci.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_dci.o --omf_browse .\objects\gd32f4xx_dci.crf --depend .\objects\gd32f4xx_dci.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_dma.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_dma.o --omf_browse .\objects\gd32f4xx_dma.crf --depend .\objects\gd32f4xx_dma.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_enet.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_enet.o --omf_browse .\objects\gd32f4xx_enet.crf --depend .\objects\gd32f4xx_enet.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exmc.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_exmc.o --omf_browse .\objects\gd32f4xx_exmc.crf --depend .\objects\gd32f4xx_exmc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_exti.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_exti.o --omf_browse .\objects\gd32f4xx_exti.crf --depend .\objects\gd32f4xx_exti.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fmc.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_fmc.o --omf_browse .\objects\gd32f4xx_fmc.crf --depend .\objects\gd32f4xx_fmc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_fwdgt.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_fwdgt.o --omf_browse .\objects\gd32f4xx_fwdgt.crf --depend .\objects\gd32f4xx_fwdgt.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_gpio.c)(0x6481A692)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_gpio.o --omf_browse .\objects\gd32f4xx_gpio.crf --depend .\objects\gd32f4xx_gpio.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_i2c.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_i2c.o --omf_browse .\objects\gd32f4xx_i2c.crf --depend .\objects\gd32f4xx_i2c.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_ipa.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_ipa.o --omf_browse .\objects\gd32f4xx_ipa.crf --depend .\objects\gd32f4xx_ipa.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_iref.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_iref.o --omf_browse .\objects\gd32f4xx_iref.crf --depend .\objects\gd32f4xx_iref.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_misc.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_misc.o --omf_browse .\objects\gd32f4xx_misc.crf --depend .\objects\gd32f4xx_misc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_pmu.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_pmu.o --omf_browse .\objects\gd32f4xx_pmu.crf --depend .\objects\gd32f4xx_pmu.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rcu.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_rcu.o --omf_browse .\objects\gd32f4xx_rcu.crf --depend .\objects\gd32f4xx_rcu.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_rtc.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_rtc.o --omf_browse .\objects\gd32f4xx_rtc.crf --depend .\objects\gd32f4xx_rtc.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_sdio.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_sdio.o --omf_browse .\objects\gd32f4xx_sdio.crf --depend .\objects\gd32f4xx_sdio.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_spi.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_spi.o --omf_browse .\objects\gd32f4xx_spi.crf --depend .\objects\gd32f4xx_spi.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_syscfg.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_syscfg.o --omf_browse .\objects\gd32f4xx_syscfg.crf --depend .\objects\gd32f4xx_syscfg.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_timer.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_timer.o --omf_browse .\objects\gd32f4xx_timer.crf --depend .\objects\gd32f4xx_timer.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_tli.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_tli.o --omf_browse .\objects\gd32f4xx_tli.crf --depend .\objects\gd32f4xx_tli.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_trng.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_trng.o --omf_browse .\objects\gd32f4xx_trng.crf --depend .\objects\gd32f4xx_trng.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_usart.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_usart.o --omf_browse .\objects\gd32f4xx_usart.crf --depend .\objects\gd32f4xx_usart.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\GD32F4xx_standard_peripheral\Source\gd32f4xx_wwdgt.c)(0x6229735B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_wwdgt.o --omf_browse .\objects\gd32f4xx_wwdgt.crf --depend .\objects\gd32f4xx_wwdgt.d)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Source\src\gd32f4xx_it.c)(0x67BFC7F8)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gd32f4xx_it.o --omf_browse .\objects\gd32f4xx_it.crf --depend .\objects\gd32f4xx_it.d)
I (..\Source\inc\gd32f4xx_it.h)(0x5CCFA4FE)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Source\inc\main.h)(0x67B6CCF9)
I (..\Source\inc\systick.h)(0x624D71D9)
I (..\bsp\inc\bsp_fmc.h)(0x62B91682)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x65619845)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\bsp\inc\bsp_tim.h)(0x6500098E)
I (..\bsp\inc\bsp_rtc.h)(0x62A81D02)
I (..\Source\inc\Time_unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\bsp\inc\Logger.h)(0x62A197B0)
I (..\bsp\inc\CH378_HAL.h)(0x62BE4517)
I (..\bsp\inc\CH378INC.H)(0x62A31FD9)
I (..\bsp\inc\FILE_SYS.h)(0x62C2910A)
I (..\bsp\inc\bsp_gpio.h)(0x66CD7DEE)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdarg.h)(0x60252376)
I (..\Source\inc\fpgad.h)(0x67B6CD21)
I (..\Source\src\appdefine.h)(0x67C95CE0)
I (..\Source\inc\FirmwareUpdateFile.h)(0x6757DCB6)
I (..\Source\inc\appmain.h)(0x6757D8E8)
I (..\bsp\inc\bsp_flash.h)(0x62481FB5)
I (..\bsp\inc\bsp_can.h)(0x652CA9F1)
I (..\bsp\inc\bsp_fwdgt.h)(0x6254D09C)
I (..\bsp\inc\CH395SPI.H)(0x62BAD73A)
I (..\bsp\inc\CH395INC.H)(0x60C2CB7F)
I (..\bsp\inc\CH395CMD.H)(0x62B95965)
I (..\Source\inc\TCPServer.h)(0x62CD0B0C)
I (..\bsp\inc\bsp_exti.h)(0x64ACAAA6)
I (..\bsp\inc\bmp280.h)(0x62A311AA)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\bsp\inc\bmp2.h)(0x60B09D03)
I (..\bsp\inc\bmp2_defs.h)(0x60B09D03)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\bsp\inc\common.h)(0x60B09D03)
I (..\NAV\nav_app.h)(0x657900AE)
I (..\Protocol\serial.h)(0x647835F6)
I (..\NAV\nav.h)(0x66B18E74)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\Protocol\computerFrameParse.h)(0x658B78A3)
I (..\Protocol\SetParaBao.h)(0x6842CEEA)
F (..\Source\src\main.c)(0x6842D0A9)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\main.o --omf_browse .\objects\main.crf --depend .\objects\main.d)
I (..\Source\inc\appmain.h)(0x6757D8E8)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Source\inc\systick.h)(0x624D71D9)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Source\inc\main.h)(0x67B6CCF9)
I (..\bsp\inc\bsp_gpio.h)(0x66CD7DEE)
I (..\bsp\inc\bsp_flash.h)(0x62481FB5)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x65619845)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\bsp\inc\bsp_rtc.h)(0x62A81D02)
I (..\Source\inc\Time_unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\bsp\inc\bsp_can.h)(0x652CA9F1)
I (..\bsp\inc\bsp_fwdgt.h)(0x6254D09C)
I (..\bsp\inc\CH395SPI.H)(0x62BAD73A)
I (..\bsp\inc\CH395INC.H)(0x60C2CB7F)
I (..\bsp\inc\CH395CMD.H)(0x62B95965)
I (..\Source\inc\TCPServer.h)(0x62CD0B0C)
I (..\bsp\inc\bsp_fmc.h)(0x62B91682)
I (..\bsp\inc\bsp_exti.h)(0x64ACAAA6)
I (..\bsp\inc\bmp280.h)(0x62A311AA)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\bsp\inc\bmp2.h)(0x60B09D03)
I (..\bsp\inc\bmp2_defs.h)(0x60B09D03)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\bsp\inc\common.h)(0x60B09D03)
I (..\bsp\inc\CH378_HAL.h)(0x62BE4517)
I (..\bsp\inc\CH378INC.H)(0x62A31FD9)
I (..\bsp\inc\logger.h)(0x62A197B0)
I (..\bsp\inc\FILE_SYS.h)(0x62C2910A)
I (..\bsp\inc\bsp_tim.h)(0x6500098E)
I (..\NAV\nav_app.h)(0x657900AE)
I (..\Source\inc\fpgad.h)(0x67B6CD21)
I (..\Source\src\appdefine.h)(0x67C95CE0)
I (..\Protocol\serial.h)(0x647835F6)
I (..\NAV\nav.h)(0x66B18E74)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\Protocol\computerFrameParse.h)(0x658B78A3)
I (..\Protocol\SetParaBao.h)(0x6842CEEA)
I (..\Source\inc\FirmwareUpdateFile.h)(0x6757DCB6)
I (..\NAV\nav_includes.h)(0x67127D5C)
I (..\NAV\nav_math.h)(0x6683A9C3)
I (..\NAV\nav_sins.h)(0x669C7908)
I (..\NAV\nav_ods.h)(0x6572C083)
I (..\NAV\nav_kf.h)(0x669A0A87)
I (..\NAV\navlog.h)(0x64A4C2AE)
I (..\NAV\nav_magnet.h)(0x64A4C2AE)
I (..\NAV\nav_mahony.h)(0x64A4E794)
I (..\NAV\nav_cli.h)(0x64A4C2AE)
I (..\NAV\nav_imu.h)(0x657F99E9)
I (..\Source\src\gdtypedefine.h)(0x66B18E74)
F (..\Source\src\systick.c)(0x62AAE42C)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\systick.o --omf_browse .\objects\systick.crf --depend .\objects\systick.d)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Source\inc\systick.h)(0x624D71D9)
F (..\Source\src\can_data.c)(0x64780369)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\can_data.o --omf_browse .\objects\can_data.crf --depend .\objects\can_data.d)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
F (..\Source\src\gnss.c)(0x67BFF776)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gnss.o --omf_browse .\objects\gnss.crf --depend .\objects\gnss.d)
I (..\Source\inc\gnss.h)(0x65619845)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\Source\inc\Time_Unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\Protocol\DRamAdapter.h)(0x63B3A701)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\Protocol\UartAdapter.h)(0x63B3A701)
I (..\Protocol\UartDefine.h)(0x63B3A701)
I (..\Source\inc\main.h)(0x67B6CCF9)
I (..\NAV\nav.h)(0x66B18E74)
I (..\Protocol\drv_rtc.h)(0x63B3A701)
F (..\Source\src\imu_data.c)(0x6256891A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\imu_data.o --omf_browse .\objects\imu_data.crf --depend .\objects\imu_data.d)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
F (..\Source\src\INS_Data.c)(0x64FFD01A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\ins_data.o --omf_browse .\objects\ins_data.crf --depend .\objects\ins_data.d)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x65619845)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\bsp\inc\bsp_flash.h)(0x62481FB5)
F (..\Source\src\INS_Sys.c)(0x6497EC52)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\ins_sys.o --omf_browse .\objects\ins_sys.crf --depend .\objects\ins_sys.d)
I (..\Source\inc\INS_Sys.h)(0x6583D1D0)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
F (..\Source\src\TCPServer.c)(0x62CE2423)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\tcpserver.o --omf_browse .\objects\tcpserver.crf --depend .\objects\tcpserver.d)
I (..\Source\inc\TCPServer.h)(0x62CD0B0C)
I (..\bsp\inc\CH395SPI.H)(0x62BAD73A)
I (..\bsp\inc\CH395INC.H)(0x60C2CB7F)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Source\inc\systick.h)(0x624D71D9)
I (..\bsp\inc\CH395CMD.H)(0x62B95965)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
F (..\Source\src\Time_Unify.c)(0x62C3A0A2)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\time_unify.o --omf_browse .\objects\time_unify.crf --depend .\objects\time_unify.d)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\Source\inc\Time_Unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
F (..\Source\src\fpgad.c)(0x67BFC194)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\fpgad.o --omf_browse .\objects\fpgad.crf --depend .\objects\fpgad.d)
I (..\Source\inc\fpgad.h)(0x67B6CD21)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x65619845)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\Source\src\appdefine.h)(0x67C95CE0)
F (..\Source\src\ymodem.c)(0x64C1D8B8)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\ymodem.o --omf_browse .\objects\ymodem.crf --depend .\objects\ymodem.d)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\Source\src\clock.h)(0x63B3A701)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\Source\inc\systick.h)(0x624D71D9)
I (..\Source\src\ymodem.h)(0x63B3A701)
I (..\Protocol\uartadapter.h)(0x63B3A701)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\Protocol\UartDefine.h)(0x63B3A701)
I (..\Protocol\fmc_operation.h)(0x64AA8C5C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Protocol\computerFrameParse.h)(0x658B78A3)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x65619845)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\Source\inc\fpgad.h)(0x67B6CD21)
I (..\Source\src\appdefine.h)(0x67C95CE0)
F (..\Source\src\clock.c)(0x63B3A701)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\clock.o --omf_browse .\objects\clock.crf --depend .\objects\clock.d)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\Source\src\clock.h)(0x63B3A701)
I (..\Protocol\insdef.h)(0x63B3A701)
F (..\Source\src\Data_shift.c)(0x628B215E)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\data_shift.o --omf_browse .\objects\data_shift.crf --depend .\objects\data_shift.d)
I (..\Source\inc\data_shift.h)(0x628B00B6)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Source\inc\ins_data.h)(0x650CEBC6)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x65619845)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
F (..\Source\src\gdwatch.c)(0x6566A78B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\gdwatch.o --omf_browse .\objects\gdwatch.crf --depend .\objects\gdwatch.d)
I (..\Source\src\gdtypedefine.h)(0x66B18E74)
I (..\Source\src\appdefine.h)(0x67C95CE0)
I (..\Source\inc\fpgad.h)(0x67B6CD21)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x65619845)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Protocol\insdef.h)(0x63B3A701)
F (..\Source\src\INS_Output.c)(0x6842813B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\ins_output.o --omf_browse .\objects\ins_output.crf --depend .\objects\ins_output.d)
I (..\Source\inc\appmain.h)(0x6757D8E8)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Source\inc\systick.h)(0x624D71D9)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Source\inc\main.h)(0x67B6CCF9)
I (..\bsp\inc\bsp_gpio.h)(0x66CD7DEE)
I (..\bsp\inc\bsp_flash.h)(0x62481FB5)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x65619845)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\bsp\inc\bsp_rtc.h)(0x62A81D02)
I (..\Source\inc\Time_unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\bsp\inc\bsp_can.h)(0x652CA9F1)
I (..\bsp\inc\bsp_fwdgt.h)(0x6254D09C)
I (..\bsp\inc\CH395SPI.H)(0x62BAD73A)
I (..\bsp\inc\CH395INC.H)(0x60C2CB7F)
I (..\bsp\inc\CH395CMD.H)(0x62B95965)
I (..\Source\inc\TCPServer.h)(0x62CD0B0C)
I (..\bsp\inc\bsp_fmc.h)(0x62B91682)
I (..\bsp\inc\bsp_exti.h)(0x64ACAAA6)
I (..\bsp\inc\bmp280.h)(0x62A311AA)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\bsp\inc\bmp2.h)(0x60B09D03)
I (..\bsp\inc\bmp2_defs.h)(0x60B09D03)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\bsp\inc\common.h)(0x60B09D03)
I (..\bsp\inc\CH378_HAL.h)(0x62BE4517)
I (..\bsp\inc\CH378INC.H)(0x62A31FD9)
I (..\bsp\inc\logger.h)(0x62A197B0)
I (..\bsp\inc\FILE_SYS.h)(0x62C2910A)
I (..\bsp\inc\bsp_tim.h)(0x6500098E)
I (..\NAV\nav_app.h)(0x657900AE)
I (..\Source\inc\fpgad.h)(0x67B6CD21)
I (..\Source\src\appdefine.h)(0x67C95CE0)
I (..\Protocol\serial.h)(0x647835F6)
I (..\NAV\nav.h)(0x66B18E74)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\Protocol\computerFrameParse.h)(0x658B78A3)
I (..\Protocol\SetParaBao.h)(0x6842CEEA)
I (..\Source\inc\FirmwareUpdateFile.h)(0x6757DCB6)
I (..\NAV\nav_includes.h)(0x67127D5C)
I (..\NAV\nav_math.h)(0x6683A9C3)
I (..\NAV\nav_sins.h)(0x669C7908)
I (..\NAV\nav_ods.h)(0x6572C083)
I (..\NAV\nav_kf.h)(0x669A0A87)
I (..\NAV\navlog.h)(0x64A4C2AE)
I (..\NAV\nav_magnet.h)(0x64A4C2AE)
I (..\NAV\nav_mahony.h)(0x64A4E794)
I (..\NAV\nav_cli.h)(0x64A4C2AE)
I (..\NAV\nav_imu.h)(0x657F99E9)
I (..\Source\src\gdtypedefine.h)(0x66B18E74)
I (..\Source\inc\INS_Output.h)(0x65224C2E)
F (..\Source\src\FirmwareUpdateFile.c)(0x67BE7335)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\firmwareupdatefile.o --omf_browse .\objects\firmwareupdatefile.crf --depend .\objects\firmwareupdatefile.d)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\Source\inc\appmain.h)(0x6757D8E8)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Source\inc\systick.h)(0x624D71D9)
I (..\Source\inc\main.h)(0x67B6CCF9)
I (..\bsp\inc\bsp_gpio.h)(0x66CD7DEE)
I (..\bsp\inc\bsp_flash.h)(0x62481FB5)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x65619845)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\bsp\inc\bsp_rtc.h)(0x62A81D02)
I (..\Source\inc\Time_unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\bsp\inc\bsp_can.h)(0x652CA9F1)
I (..\bsp\inc\bsp_fwdgt.h)(0x6254D09C)
I (..\bsp\inc\CH395SPI.H)(0x62BAD73A)
I (..\bsp\inc\CH395INC.H)(0x60C2CB7F)
I (..\bsp\inc\CH395CMD.H)(0x62B95965)
I (..\Source\inc\TCPServer.h)(0x62CD0B0C)
I (..\bsp\inc\bsp_fmc.h)(0x62B91682)
I (..\bsp\inc\bsp_exti.h)(0x64ACAAA6)
I (..\bsp\inc\bmp280.h)(0x62A311AA)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\bsp\inc\bmp2.h)(0x60B09D03)
I (..\bsp\inc\bmp2_defs.h)(0x60B09D03)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\bsp\inc\common.h)(0x60B09D03)
I (..\bsp\inc\CH378_HAL.h)(0x62BE4517)
I (..\bsp\inc\CH378INC.H)(0x62A31FD9)
I (..\bsp\inc\logger.h)(0x62A197B0)
I (..\bsp\inc\FILE_SYS.h)(0x62C2910A)
I (..\bsp\inc\bsp_tim.h)(0x6500098E)
I (..\NAV\nav_app.h)(0x657900AE)
I (..\Source\inc\fpgad.h)(0x67B6CD21)
I (..\Source\src\appdefine.h)(0x67C95CE0)
I (..\Protocol\serial.h)(0x647835F6)
I (..\NAV\nav.h)(0x66B18E74)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\Protocol\computerFrameParse.h)(0x658B78A3)
I (..\Protocol\SetParaBao.h)(0x6842CEEA)
I (..\Source\inc\FirmwareUpdateFile.h)(0x6757DCB6)
F (..\bsp\src\bmp2.c)(0x62A3063B)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\bmp2.o --omf_browse .\objects\bmp2.crf --depend .\objects\bmp2.d)
I (..\bsp\inc\bmp2.h)(0x60B09D03)
I (..\bsp\inc\bmp2_defs.h)(0x60B09D03)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
F (..\bsp\src\bmp280.c)(0x62C4FB5D)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\bmp280.o --omf_browse .\objects\bmp280.crf --depend .\objects\bmp280.d)
I (..\bsp\inc\bmp280.h)(0x62A311AA)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\bsp\inc\bmp2.h)(0x60B09D03)
I (..\bsp\inc\bmp2_defs.h)(0x60B09D03)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\bsp\inc\common.h)(0x60B09D03)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
F (..\bsp\src\bsp_adc.c)(0x628B6228)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\bsp_adc.o --omf_browse .\objects\bsp_adc.crf --depend .\objects\bsp_adc.d)
I (..\bsp\inc\bsp_adc.h)(0x628B4F68)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Source\inc\systick.h)(0x624D71D9)
F (..\bsp\src\bsp_can.c)(0x682D2CC9)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\bsp_can.o --omf_browse .\objects\bsp_can.crf --depend .\objects\bsp_can.d)
I (..\bsp\inc\bsp_can.h)(0x652CA9F1)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x65619845)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\bsp\inc\bsp_gpio.h)(0x66CD7DEE)
I (..\bsp\inc\bsp_tim.h)(0x6500098E)
I (..\Source\inc\Time_Unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\Source\inc\fpgad.h)(0x67B6CD21)
I (..\Source\src\appdefine.h)(0x67C95CE0)
F (..\bsp\src\bsp_exti.c)(0x67C16CE3)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\bsp_exti.o --omf_browse .\objects\bsp_exti.crf --depend .\objects\bsp_exti.d)
I (..\bsp\inc\bsp_exti.h)(0x64ACAAA6)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\bsp\src\bsp_flash.c)(0x6497F2D9)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\bsp_flash.o --omf_browse .\objects\bsp_flash.crf --depend .\objects\bsp_flash.d)
I (..\bsp\inc\bsp_flash.h)(0x62481FB5)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\Source\inc\main.h)(0x67B6CCF9)
F (..\bsp\src\bsp_fmc.c)(0x656718AC)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\bsp_fmc.o --omf_browse .\objects\bsp_fmc.crf --depend .\objects\bsp_fmc.d)
I (..\bsp\inc\bsp_fmc.h)(0x62B91682)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Source\inc\systick.h)(0x624D71D9)
I (..\Source\inc\fpgad.h)(0x67B6CD21)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x65619845)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\Source\src\appdefine.h)(0x67C95CE0)
F (..\bsp\src\bsp_fwdgt.c)(0x62CD2532)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\bsp_fwdgt.o --omf_browse .\objects\bsp_fwdgt.crf --depend .\objects\bsp_fwdgt.d)
I (..\bsp\inc\bsp_fwdgt.h)(0x6254D09C)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\bsp\src\bsp_gpio.c)(0x647D474D)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\bsp_gpio.o --omf_browse .\objects\bsp_gpio.crf --depend .\objects\bsp_gpio.d)
I (..\bsp\inc\bsp_gpio.h)(0x66CD7DEE)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\bsp\src\bsp_rtc.c)(0x62A9516D)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\bsp_rtc.o --omf_browse .\objects\bsp_rtc.crf --depend .\objects\bsp_rtc.d)
I (..\bsp\inc\bsp_rtc.h)(0x62A81D02)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\Source\inc\Time_unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
F (..\bsp\src\bsp_sys.c)(0x6264C948)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\bsp_sys.o --omf_browse .\objects\bsp_sys.crf --depend .\objects\bsp_sys.d)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\bsp\src\bsp_tim.c)(0x65000B52)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\bsp_tim.o --omf_browse .\objects\bsp_tim.crf --depend .\objects\bsp_tim.d)
I (..\bsp\inc\bsp_tim.h)(0x6500098E)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\bsp\src\bsp_uart.c)(0x656007AE)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\bsp_uart.o --omf_browse .\objects\bsp_uart.crf --depend .\objects\bsp_uart.d)
I (..\bsp\inc\bsp_uart.h)(0x6245473A)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Source\inc\fpgad.h)(0x67B6CD21)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x65619845)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\Source\src\appdefine.h)(0x67C95CE0)
F (..\bsp\src\CH378_HAL.C)(0x62958CE3)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\ch378_hal.o --omf_browse .\objects\ch378_hal.crf --depend .\objects\ch378_hal.d)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\bsp\inc\TYPE.H)(0x628CAEC2)
I (..\bsp\inc\CH378_HAL.H)(0x62BE4517)
I (..\bsp\inc\CH378INC.H)(0x62A31FD9)
I (..\Source\inc\systick.h)(0x624D71D9)
F (..\bsp\src\CH395CMD.C)(0x62CCD953)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\ch395cmd.o --omf_browse .\objects\ch395cmd.crf --depend .\objects\ch395cmd.d)
I (..\bsp\inc\CH395INC.H)(0x60C2CB7F)
I (..\bsp\inc\ch395cmd.h)(0x62B95965)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\bsp\inc\CH395SPI.H)(0x62BAD73A)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Source\inc\systick.h)(0x624D71D9)
F (..\bsp\src\CH395SPI.C)(0x62CD237A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\ch395spi.o --omf_browse .\objects\ch395spi.crf --depend .\objects\ch395spi.d)
I (..\bsp\inc\CH395SPI.H)(0x62BAD73A)
I (..\bsp\inc\CH395INC.H)(0x60C2CB7F)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Source\inc\systick.h)(0x624D71D9)
F (..\bsp\src\common.c)(0x62C52125)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\common.o --omf_browse .\objects\common.crf --depend .\objects\common.d)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Source\inc\systick.h)(0x624D71D9)
I (..\bsp\inc\bmp2_defs.h)(0x60B09D03)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\bsp\inc\bsp_soft_i2c_master.h)(0x624D41E3)
I (..\bsp\inc\soft_i2c.h)(0x624CFB4C)
F (..\bsp\src\FILE_SYS.C)(0x62BED493)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\file_sys.o --omf_browse .\objects\file_sys.crf --depend .\objects\file_sys.d)
I (..\bsp\inc\FILE_SYS.H)(0x62C2910A)
I (..\bsp\inc\CH378_HAL.H)(0x62BE4517)
I (..\bsp\inc\CH378INC.H)(0x62A31FD9)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\bsp\src\Logger.c)(0x62CBD092)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\logger.o --omf_browse .\objects\logger.crf --depend .\objects\logger.d)
I (..\bsp\inc\logger.h)(0x62A197B0)
I (..\bsp\inc\CH378_HAL.h)(0x62BE4517)
I (..\bsp\inc\CH378INC.H)(0x62A31FD9)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\bsp\inc\FILE_SYS.h)(0x62C2910A)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\bsp\inc\bsp_rtc.h)(0x62A81D02)
I (..\Source\inc\Time_unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Source\inc\gnss.h)(0x65619845)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
F (..\bsp\src\TCP_Server.c)(0x6295E26D)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\tcp_server.o --omf_browse .\objects\tcp_server.crf --depend .\objects\tcp_server.d)
I (..\bsp\inc\TCP_Server.h)(0x6295E258)
F (..\bsp\src\CH378_SPI_HW.C)(0x62CBD0BC)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\ch378_spi_hw.o --omf_browse .\objects\ch378_spi_hw.crf --depend .\objects\ch378_spi_hw.d)
I (..\bsp\inc\CH378_HAL.h)(0x62BE4517)
I (..\bsp\inc\CH378INC.H)(0x62A31FD9)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Source\inc\systick.h)(0x624D71D9)
I (..\bsp\inc\FILE_SYS.h)(0x62C2910A)
F (..\bsp\src\bsp_soft_i2c_master.c)(0x62C4FA22)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\bsp_soft_i2c_master.o --omf_browse .\objects\bsp_soft_i2c_master.crf --depend .\objects\bsp_soft_i2c_master.d)
I (..\bsp\inc\bsp_soft_i2c_master.h)(0x624D41E3)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\bsp\inc\soft_i2c.h)(0x624CFB4C)
F (..\Library\CMSIS\GD\GD32F4xx\Source\ARM\startup_gd32f450_470.s)(0x64A90C06)(--cpu Cortex-M4.fp.sp -g --apcs=interwork --pd "__MICROLIB SETA 1"

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

--pd "__UVISION_VERSION SETA 536" --pd "GD32F470 SETA 1"

--list .\listings\startup_gd32f450_470.lst --xref -o .\objects\startup_gd32f450_470.o --depend .\objects\startup_gd32f450_470.d)
F (..\Library\CMSIS\GD\GD32F4xx\Source\system_gd32f4xx.c)(0x62A8454C)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\system_gd32f4xx.o --omf_browse .\objects\system_gd32f4xx.crf --depend .\objects\system_gd32f4xx.d)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
F (..\Library\CMSIS\arm_cortexM4lf_math.lib)(0x581CC65F)()
F (..\Common\src\data_convert.c)(0x62A853B6)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\data_convert.o --omf_browse .\objects\data_convert.crf --depend .\objects\data_convert.d)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
F (..\RTT\SEGGER_RTT.c)(0x60DDE3ED)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\segger_rtt.o --omf_browse .\objects\segger_rtt.crf --depend .\objects\segger_rtt.d)
I (..\RTT\SEGGER_RTT.h)(0x60DDE3ED)
I (..\RTT\SEGGER_RTT_Conf.h)(0x60DDE3ED)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdarg.h)(0x60252376)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
F (..\RTT\SEGGER_RTT_printf.c)(0x60DDE3ED)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\segger_rtt_printf.o --omf_browse .\objects\segger_rtt_printf.crf --depend .\objects\segger_rtt_printf.d)
I (..\RTT\SEGGER_RTT.h)(0x60DDE3ED)
I (..\RTT\SEGGER_RTT_Conf.h)(0x60DDE3ED)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdarg.h)(0x60252376)
F (..\NAV\nav.c)(0x67763865)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\nav.o --omf_browse .\objects\nav.crf --depend .\objects\nav.d)
I (..\NAV\nav_includes.h)(0x67127D5C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\NAV\nav_math.h)(0x6683A9C3)
I (..\NAV\nav_sins.h)(0x669C7908)
I (..\NAV\nav_ods.h)(0x6572C083)
I (..\NAV\nav_kf.h)(0x669A0A87)
I (..\NAV\nav_app.h)(0x657900AE)
I (..\NAV\nav.h)(0x66B18E74)
I (..\NAV\navlog.h)(0x64A4C2AE)
I (..\NAV\nav_magnet.h)(0x64A4C2AE)
I (..\NAV\nav_mahony.h)(0x64A4E794)
I (..\NAV\nav_cli.h)(0x64A4C2AE)
I (..\NAV\nav_imu.h)(0x657F99E9)
F (..\NAV\nav_app.c)(0x6842D011)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\nav_app.o --omf_browse .\objects\nav_app.crf --depend .\objects\nav_app.d)
I (..\NAV\nav_includes.h)(0x67127D5C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\NAV\nav_math.h)(0x6683A9C3)
I (..\NAV\nav_sins.h)(0x669C7908)
I (..\NAV\nav_ods.h)(0x6572C083)
I (..\NAV\nav_kf.h)(0x669A0A87)
I (..\NAV\nav_app.h)(0x657900AE)
I (..\NAV\nav.h)(0x66B18E74)
I (..\NAV\navlog.h)(0x64A4C2AE)
I (..\NAV\nav_magnet.h)(0x64A4C2AE)
I (..\NAV\nav_mahony.h)(0x64A4E794)
I (..\NAV\nav_cli.h)(0x64A4C2AE)
I (..\NAV\nav_imu.h)(0x657F99E9)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Source\inc\gnss.h)(0x65619845)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\Protocol\SetParaBao.h)(0x6842CEEA)
I (..\Source\inc\fpgad.h)(0x67B6CD21)
I (..\Source\src\appdefine.h)(0x67C95CE0)
I (..\Protocol\UartAdapter.h)(0x63B3A701)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\Protocol\UartDefine.h)(0x63B3A701)
F (..\NAV\nav_kf.c)(0x67885603)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\nav_kf.o --omf_browse .\objects\nav_kf.crf --depend .\objects\nav_kf.d)
I (..\NAV\nav_includes.h)(0x67127D5C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\NAV\nav_math.h)(0x6683A9C3)
I (..\NAV\nav_sins.h)(0x669C7908)
I (..\NAV\nav_ods.h)(0x6572C083)
I (..\NAV\nav_kf.h)(0x669A0A87)
I (..\NAV\nav_app.h)(0x657900AE)
I (..\NAV\nav.h)(0x66B18E74)
I (..\NAV\navlog.h)(0x64A4C2AE)
I (..\NAV\nav_magnet.h)(0x64A4C2AE)
I (..\NAV\nav_mahony.h)(0x64A4E794)
I (..\NAV\nav_cli.h)(0x64A4C2AE)
I (..\NAV\nav_imu.h)(0x657F99E9)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\Protocol\computerFrameParse.h)(0x658B78A3)
I (..\Protocol\config.h)(0x64AB57C0)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (..\Source\inc\gnss.h)(0x65619845)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
F (..\NAV\nav_math.c)(0x66AAF5AF)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\nav_math.o --omf_browse .\objects\nav_math.crf --depend .\objects\nav_math.d)
I (..\NAV\NAV_Includes.h)(0x67127D5C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\NAV\nav_math.h)(0x6683A9C3)
I (..\NAV\nav_sins.h)(0x669C7908)
I (..\NAV\nav_ods.h)(0x6572C083)
I (..\NAV\nav_kf.h)(0x669A0A87)
I (..\NAV\nav_app.h)(0x657900AE)
I (..\NAV\nav.h)(0x66B18E74)
I (..\NAV\navlog.h)(0x64A4C2AE)
I (..\NAV\nav_magnet.h)(0x64A4C2AE)
I (..\NAV\nav_mahony.h)(0x64A4E794)
I (..\NAV\nav_cli.h)(0x64A4C2AE)
I (..\NAV\nav_imu.h)(0x657F99E9)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
F (..\NAV\nav_ods.c)(0x67A95317)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\nav_ods.o --omf_browse .\objects\nav_ods.crf --depend .\objects\nav_ods.d)
I (..\NAV\nav_includes.h)(0x67127D5C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\NAV\nav_math.h)(0x6683A9C3)
I (..\NAV\nav_sins.h)(0x669C7908)
I (..\NAV\nav_ods.h)(0x6572C083)
I (..\NAV\nav_kf.h)(0x669A0A87)
I (..\NAV\nav_app.h)(0x657900AE)
I (..\NAV\nav.h)(0x66B18E74)
I (..\NAV\navlog.h)(0x64A4C2AE)
I (..\NAV\nav_magnet.h)(0x64A4C2AE)
I (..\NAV\nav_mahony.h)(0x64A4E794)
I (..\NAV\nav_cli.h)(0x64A4C2AE)
I (..\NAV\nav_imu.h)(0x657F99E9)
F (..\NAV\nav_sins.c)(0x675704A6)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\nav_sins.o --omf_browse .\objects\nav_sins.crf --depend .\objects\nav_sins.d)
I (..\NAV\nav_includes.h)(0x67127D5C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\NAV\nav_math.h)(0x6683A9C3)
I (..\NAV\nav_sins.h)(0x669C7908)
I (..\NAV\nav_ods.h)(0x6572C083)
I (..\NAV\nav_kf.h)(0x669A0A87)
I (..\NAV\nav_app.h)(0x657900AE)
I (..\NAV\nav.h)(0x66B18E74)
I (..\NAV\navlog.h)(0x64A4C2AE)
I (..\NAV\nav_magnet.h)(0x64A4C2AE)
I (..\NAV\nav_mahony.h)(0x64A4E794)
I (..\NAV\nav_cli.h)(0x64A4C2AE)
I (..\NAV\nav_imu.h)(0x657F99E9)
F (..\NAV\navlog.c)(0x66F62B3C)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\navlog.o --omf_browse .\objects\navlog.crf --depend .\objects\navlog.d)
I (..\NAV\navlog.h)(0x64A4C2AE)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdarg.h)(0x60252376)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\NAV\nav_includes.h)(0x67127D5C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\NAV\nav_math.h)(0x6683A9C3)
I (..\NAV\nav_sins.h)(0x669C7908)
I (..\NAV\nav_ods.h)(0x6572C083)
I (..\NAV\nav_kf.h)(0x669A0A87)
I (..\NAV\nav_app.h)(0x657900AE)
I (..\NAV\nav.h)(0x66B18E74)
I (..\NAV\nav_magnet.h)(0x64A4C2AE)
I (..\NAV\nav_mahony.h)(0x64A4E794)
I (..\NAV\nav_cli.h)(0x64A4C2AE)
I (..\NAV\nav_imu.h)(0x657F99E9)
F (..\NAV\nav_cli.c)(0x67DA7CE8)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\nav_cli.o --omf_browse .\objects\nav_cli.crf --depend .\objects\nav_cli.d)
I (..\NAV\nav_includes.h)(0x67127D5C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\NAV\nav_math.h)(0x6683A9C3)
I (..\NAV\nav_sins.h)(0x669C7908)
I (..\NAV\nav_ods.h)(0x6572C083)
I (..\NAV\nav_kf.h)(0x669A0A87)
I (..\NAV\nav_app.h)(0x657900AE)
I (..\NAV\nav.h)(0x66B18E74)
I (..\NAV\navlog.h)(0x64A4C2AE)
I (..\NAV\nav_magnet.h)(0x64A4C2AE)
I (..\NAV\nav_mahony.h)(0x64A4E794)
I (..\NAV\nav_cli.h)(0x64A4C2AE)
I (..\NAV\nav_imu.h)(0x657F99E9)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Protocol\UartAdapter.h)(0x63B3A701)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\Protocol\UartDefine.h)(0x63B3A701)
I (..\Protocol\computerFrameParse.h)(0x658B78A3)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (..\Source\inc\gnss.h)(0x65619845)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
F (..\NAV\nav_gnss.c)(0x671225C5)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\nav_gnss.o --omf_browse .\objects\nav_gnss.crf --depend .\objects\nav_gnss.d)
I (..\NAV\nav_includes.h)(0x67127D5C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\NAV\nav_math.h)(0x6683A9C3)
I (..\NAV\nav_sins.h)(0x669C7908)
I (..\NAV\nav_ods.h)(0x6572C083)
I (..\NAV\nav_kf.h)(0x669A0A87)
I (..\NAV\nav_app.h)(0x657900AE)
I (..\NAV\nav.h)(0x66B18E74)
I (..\NAV\navlog.h)(0x64A4C2AE)
I (..\NAV\nav_magnet.h)(0x64A4C2AE)
I (..\NAV\nav_mahony.h)(0x64A4E794)
I (..\NAV\nav_cli.h)(0x64A4C2AE)
I (..\NAV\nav_imu.h)(0x657F99E9)
F (..\NAV\nav_imu.c)(0x6842CF51)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\nav_imu.o --omf_browse .\objects\nav_imu.crf --depend .\objects\nav_imu.d)
I (..\NAV\nav_includes.h)(0x67127D5C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\NAV\nav_math.h)(0x6683A9C3)
I (..\NAV\nav_sins.h)(0x669C7908)
I (..\NAV\nav_ods.h)(0x6572C083)
I (..\NAV\nav_kf.h)(0x669A0A87)
I (..\NAV\nav_app.h)(0x657900AE)
I (..\NAV\nav.h)(0x66B18E74)
I (..\NAV\navlog.h)(0x64A4C2AE)
I (..\NAV\nav_magnet.h)(0x64A4C2AE)
I (..\NAV\nav_mahony.h)(0x64A4E794)
I (..\NAV\nav_cli.h)(0x64A4C2AE)
I (..\NAV\nav_imu.h)(0x657F99E9)
I (..\Protocol\SetParaBao.h)(0x6842CEEA)
I (..\Source\inc\fpgad.h)(0x67B6CD21)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\Source\inc\gnss.h)(0x65619845)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\Source\src\appdefine.h)(0x67C95CE0)
F (..\NAV\nav_magnet.c)(0x64A4C2AE)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\nav_magnet.o --omf_browse .\objects\nav_magnet.crf --depend .\objects\nav_magnet.d)
I (..\NAV\nav_includes.h)(0x67127D5C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\NAV\nav_math.h)(0x6683A9C3)
I (..\NAV\nav_sins.h)(0x669C7908)
I (..\NAV\nav_ods.h)(0x6572C083)
I (..\NAV\nav_kf.h)(0x669A0A87)
I (..\NAV\nav_app.h)(0x657900AE)
I (..\NAV\nav.h)(0x66B18E74)
I (..\NAV\navlog.h)(0x64A4C2AE)
I (..\NAV\nav_magnet.h)(0x64A4C2AE)
I (..\NAV\nav_mahony.h)(0x64A4E794)
I (..\NAV\nav_cli.h)(0x64A4C2AE)
I (..\NAV\nav_imu.h)(0x657F99E9)
F (..\NAV\nav_mahony.c)(0x64F83D2A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\nav_mahony.o --omf_browse .\objects\nav_mahony.crf --depend .\objects\nav_mahony.d)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\NAV\nav_includes.h)(0x67127D5C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\NAV\nav_math.h)(0x6683A9C3)
I (..\NAV\nav_sins.h)(0x669C7908)
I (..\NAV\nav_ods.h)(0x6572C083)
I (..\NAV\nav_kf.h)(0x669A0A87)
I (..\NAV\nav_app.h)(0x657900AE)
I (..\NAV\nav.h)(0x66B18E74)
I (..\NAV\navlog.h)(0x64A4C2AE)
I (..\NAV\nav_magnet.h)(0x64A4C2AE)
I (..\NAV\nav_mahony.h)(0x64A4E794)
I (..\NAV\nav_cli.h)(0x64A4C2AE)
I (..\NAV\nav_imu.h)(0x657F99E9)
F (..\NAV\nav_uwb.c)(0x64A4C2AE)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\nav_uwb.o --omf_browse .\objects\nav_uwb.crf --depend .\objects\nav_uwb.d)
I (..\NAV\nav_includes.h)(0x67127D5C)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\NAV\nav_math.h)(0x6683A9C3)
I (..\NAV\nav_sins.h)(0x669C7908)
I (..\NAV\nav_ods.h)(0x6572C083)
I (..\NAV\nav_kf.h)(0x669A0A87)
I (..\NAV\nav_app.h)(0x657900AE)
I (..\NAV\nav.h)(0x66B18E74)
I (..\NAV\navlog.h)(0x64A4C2AE)
I (..\NAV\nav_magnet.h)(0x64A4C2AE)
I (..\NAV\nav_mahony.h)(0x64A4E794)
I (..\NAV\nav_cli.h)(0x64A4C2AE)
I (..\NAV\nav_imu.h)(0x657F99E9)
F (..\Protocol\computerFrameParse.c)(0x675AB14A)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\computerframeparse.o --omf_browse .\objects\computerframeparse.crf --depend .\objects\computerframeparse.d)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Protocol\computerFrameParse.h)(0x658B78A3)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Protocol\config.h)(0x64AB57C0)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\Source\inc\gnss.h)(0x65619845)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\Protocol\serial.h)(0x647835F6)
I (..\Protocol\uartadapter.h)(0x63B3A701)
I (..\Protocol\UartDefine.h)(0x63B3A701)
I (..\Protocol\drv_timer.h)(0x63B3A701)
I (..\Protocol\protocol.h)(0x63B3A701)
I (..\Source\inc\systick.h)(0x624D71D9)
I (..\NAV\nav.h)(0x66B18E74)
I (..\Source\inc\fpgad.h)(0x67B6CD21)
I (..\Source\src\appdefine.h)(0x67C95CE0)
I (..\Protocol\SetParaBao.h)(0x6842CEEA)
I (..\Protocol\calibration.h)(0x63B3A701)
I (..\Protocol\fmc_operation.h)(0x64AA8C5C)
F (..\Protocol\frame_analysis.c)(0x68428815)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\frame_analysis.o --omf_browse .\objects\frame_analysis.crf --depend .\objects\frame_analysis.d)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (..\Source\inc\gnss.h)(0x65619845)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\Protocol\drv_rtc.h)(0x63B3A701)
I (..\Protocol\serial.h)(0x647835F6)
I (..\Protocol\uartadapter.h)(0x63B3A701)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\Protocol\UartDefine.h)(0x63B3A701)
I (..\Protocol\computerFrameParse.h)(0x658B78A3)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\NAV\nav.h)(0x66B18E74)
I (..\bsp\inc\bsp_can.h)(0x652CA9F1)
I (..\Source\src\gdtypedefine.h)(0x66B18E74)
I (..\Source\src\appdefine.h)(0x67C95CE0)
I (..\Protocol\SetParaBao.h)(0x6842CEEA)
I (..\Source\inc\fpgad.h)(0x67B6CD21)
F (..\Protocol\protocol.c)(0x6758EC4E)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\protocol.o --omf_browse .\objects\protocol.crf --depend .\objects\protocol.d)
I (..\Protocol\computerFrameParse.h)(0x658B78A3)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Protocol\config.h)(0x64AB57C0)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x65619845)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\Protocol\protocol.h)(0x63B3A701)
I (..\Protocol\serial.h)(0x647835F6)
I (..\Protocol\uartadapter.h)(0x63B3A701)
I (..\Protocol\UartDefine.h)(0x63B3A701)
I (..\Protocol\SetParaBao.h)(0x6842CEEA)
I (..\Source\inc\fpgad.h)(0x67B6CD21)
I (..\Source\src\appdefine.h)(0x67C95CE0)
I (..\NAV\nav.h)(0x66B18E74)
I (..\NAV\nav_cli.h)(0x64A4C2AE)
I (..\Source\src\gdtypedefine.h)(0x66B18E74)
F (..\Protocol\transplant.c)(0x64784FD8)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\transplant.o --omf_browse .\objects\transplant.crf --depend .\objects\transplant.d)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (..\Source\inc\main.h)(0x67B6CCF9)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Protocol\transplant.h)(0x64784FC0)
I (..\Protocol\drv_rtc.h)(0x63B3A701)
I (..\Protocol\insdef.h)(0x63B3A701)
F (..\Protocol\SetParaBao.c)(0x6842CF19)(--c99 -c --cpu Cortex-M4.fp.sp -D__MICROLIB -g -O0 --apcs=interwork --split_sections -I ..\Library\CMSIS -I ..\Library\CMSIS\GD\GD32F4xx\Source\ARM -I ..\Library\CMSIS\GD\GD32F4xx\Include -I ..\Library\GD32F4xx_standard_peripheral\Include -I ..\bsp\inc -I ..\Source\inc -I ..\Common\inc -I ..\RTT -I ..\NAV -I ..\Source\src -I ..\Protocol

-ID:\softwawe\Keil_v5\GigaDevice\GD32F4xx_DFP\3.0.3\Device\F4XX\Include

-D__UVISION_VERSION="536" -DGD32F470 -DUSE_STDPERIPH_DRIVER -DGD32F470 -D__TARGET_FPU_VFP -DARM_MATH_CM4 -D__CC_ARM -D__FPU_PRESENT -D__GD32_INS

-o .\objects\setparabao.o --omf_browse .\objects\setparabao.crf --depend .\objects\setparabao.d)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdio.h)(0x60252374)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\string.h)(0x6025237E)
I (..\Source\inc\appmain.h)(0x6757D8E8)
I (..\NAV\algorithm.h)(0x67B6CB21)
I (..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdint.h)(0x6025237E)
I (..\Library\CMSIS\core_cmInstr.h)(0x6229735B)
I (..\Library\CMSIS\core_cmFunc.h)(0x6229735B)
I (..\Library\CMSIS\core_cm4_simd.h)(0x6229735B)
I (..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h)(0x6229735B)
I (..\Source\inc\gd32f4xx_libopt.h)(0x62453247)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h)(0x6229735B)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdlib.h)(0x60252374)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h)(0x6229735B)
I (..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h)(0x6229735B)
I (..\Source\inc\systick.h)(0x624D71D9)
I (..\Source\inc\main.h)(0x67B6CCF9)
I (..\bsp\inc\bsp_gpio.h)(0x66CD7DEE)
I (..\bsp\inc\bsp_flash.h)(0x62481FB5)
I (..\Source\inc\INS_Data.h)(0x650CEBC6)
I (..\Library\CMSIS\arm_math.h)(0x581CC64E)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\math.h)(0x60252378)
I (..\Source\inc\gnss.h)(0x65619845)
I (..\Common\inc\data_convert.h)(0x62A843FD)
I (..\Protocol\frame_analysis.h)(0x684287D0)
I (..\Protocol\insdef.h)(0x63B3A701)
I (..\Source\inc\tlhtype.h)(0x684287C6)
I (..\Source\inc\can_data.h)(0x64780369)
I (..\Source\inc\imu_data.h)(0x6258C36C)
I (..\Source\inc\INS_sys.h)(0x6583D1D0)
I (..\NAV\nav_type.h)(0x67CFDA04)
I (..\NAV\nav_const.h)(0x677CE9A0)
I (..\bsp\inc\bsp_sys.h)(0x6264C988)
I (..\bsp\inc\bsp_rtc.h)(0x62A81D02)
I (..\Source\inc\Time_unify.h)(0x62C3A062)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\time.h)(0x60252378)
I (..\bsp\inc\bsp_can.h)(0x652CA9F1)
I (..\bsp\inc\bsp_fwdgt.h)(0x6254D09C)
I (..\bsp\inc\CH395SPI.H)(0x62BAD73A)
I (..\bsp\inc\CH395INC.H)(0x60C2CB7F)
I (..\bsp\inc\CH395CMD.H)(0x62B95965)
I (..\Source\inc\TCPServer.h)(0x62CD0B0C)
I (..\bsp\inc\bsp_fmc.h)(0x62B91682)
I (..\bsp\inc\bsp_exti.h)(0x64ACAAA6)
I (..\bsp\inc\bmp280.h)(0x62A311AA)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stdbool.h)(0x6025237C)
I (..\bsp\inc\bmp2.h)(0x60B09D03)
I (..\bsp\inc\bmp2_defs.h)(0x60B09D03)
I (D:\softwawe\Keil_v5\ARM\ARMCC\include\stddef.h)(0x6025237E)
I (..\bsp\inc\common.h)(0x60B09D03)
I (..\bsp\inc\CH378_HAL.h)(0x62BE4517)
I (..\bsp\inc\CH378INC.H)(0x62A31FD9)
I (..\bsp\inc\logger.h)(0x62A197B0)
I (..\bsp\inc\FILE_SYS.h)(0x62C2910A)
I (..\bsp\inc\bsp_tim.h)(0x6500098E)
I (..\NAV\nav_app.h)(0x657900AE)
I (..\Source\inc\fpgad.h)(0x67B6CD21)
I (..\Source\src\appdefine.h)(0x67C95CE0)
I (..\Protocol\serial.h)(0x647835F6)
I (..\NAV\nav.h)(0x66B18E74)
I (..\Protocol\config.h)(0x64AB57C0)
I (..\Protocol\computerFrameParse.h)(0x658B78A3)
I (..\Protocol\SetParaBao.h)(0x6842CEEA)
I (..\Source\inc\FirmwareUpdateFile.h)(0x6757DCB6)
