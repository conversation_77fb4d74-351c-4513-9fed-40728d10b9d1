/***********************************************************************************
航向角掉电保存功能测试程序
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|AI Assistant    |  2025-1-27         | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/

#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#include "SetParaBao.h"
#include "nav_type.h"

// 测试用的外部变量声明
extern _NAV_Data_Full_t NAV_Data_Full;
extern Setpara_Data stSetPara;

// 测试函数声明
void test_azimuth_save_restore(void);
void test_azimuth_validation(void);
void test_azimuth_multiple_saves(void);
void print_test_result(const char* test_name, int passed);

// 主测试函数
void test_azimuth_save_restore(void)
{
    printf("=== 航向角掉电保存功能测试 ===\n\n");
    
    // 测试1: 基本保存和恢复功能
    printf("测试1: 基本保存和恢复功能\n");
    
    // 初始化航向角保存系统
    InitAzimuthSaveSystem();
    
    // 设置测试航向角值 (45度 = π/4 弧度)
    double test_azimuth = M_PI / 4.0;  // 45度
    printf("保存航向角: %.6f 弧度 (%.2f 度)\n", test_azimuth, test_azimuth * 180.0 / M_PI);
    
    // 保存航向角
    SaveAzimuthToFlash(test_azimuth);
    
    // 验证保存是否成功
    int is_valid = IsAzimuthSaveValid();
    printf("保存有效性检查: %s\n", is_valid ? "有效" : "无效");
    
    // 恢复航向角
    double restored_azimuth = RestoreAzimuthFromFlash();
    printf("恢复航向角: %.6f 弧度 (%.2f 度)\n", restored_azimuth, restored_azimuth * 180.0 / M_PI);
    
    // 检查是否匹配
    double diff = fabs(test_azimuth - restored_azimuth);
    int test1_passed = (diff < 1e-6) && is_valid;
    print_test_result("基本保存和恢复", test1_passed);
    
    printf("\n");
}

void test_azimuth_validation(void)
{
    printf("测试2: 航向角有效性验证\n");
    
    // 清除有效标志，模拟首次使用
    stSetPara.AzimuthValidFlag = 0;
    
    // 检查无效状态
    int is_valid_before = IsAzimuthSaveValid();
    printf("清除标志后有效性: %s\n", is_valid_before ? "有效" : "无效");
    
    // 恢复应该返回0
    double restored_invalid = RestoreAzimuthFromFlash();
    printf("无效状态下恢复值: %.6f 弧度\n", restored_invalid);
    
    // 保存一个新值
    double new_azimuth = M_PI / 2.0;  // 90度
    SaveAzimuthToFlash(new_azimuth);
    
    // 现在应该有效
    int is_valid_after = IsAzimuthSaveValid();
    printf("保存后有效性: %s\n", is_valid_after ? "有效" : "无效");
    
    int test2_passed = (!is_valid_before) && (restored_invalid == 0.0) && is_valid_after;
    print_test_result("有效性验证", test2_passed);
    
    printf("\n");
}

void test_azimuth_multiple_saves(void)
{
    printf("测试3: 多次保存测试\n");
    
    // 测试多个不同的航向角值
    double test_angles[] = {0.0, M_PI/6, M_PI/4, M_PI/3, M_PI/2, M_PI, 3*M_PI/2, 2*M_PI-0.1};
    int num_tests = sizeof(test_angles) / sizeof(test_angles[0]);
    int all_passed = 1;
    
    for (int i = 0; i < num_tests; i++) {
        double angle = test_angles[i];
        
        // 保存角度
        SaveAzimuthToFlash(angle);
        
        // 恢复角度
        double restored = RestoreAzimuthFromFlash();
        
        // 检查差异
        double diff = fabs(angle - restored);
        int passed = (diff < 1e-6);
        
        printf("角度 %.2f° -> 恢复 %.2f°, 差异: %.2e, %s\n", 
               angle * 180.0 / M_PI, 
               restored * 180.0 / M_PI, 
               diff,
               passed ? "通过" : "失败");
        
        if (!passed) all_passed = 0;
    }
    
    print_test_result("多次保存测试", all_passed);
    
    printf("\n");
}

void test_azimuth_counter(void)
{
    printf("测试4: 保存计数器测试\n");
    
    // 获取初始计数
    uint32_t initial_count = stSetPara.AzimuthSaveCount;
    printf("初始保存计数: %lu\n", (unsigned long)initial_count);
    
    // 进行几次保存
    for (int i = 0; i < 5; i++) {
        double angle = (double)i * M_PI / 10.0;
        SaveAzimuthToFlash(angle);
    }
    
    // 检查计数是否增加
    uint32_t final_count = stSetPara.AzimuthSaveCount;
    printf("最终保存计数: %lu\n", (unsigned long)final_count);
    
    int test4_passed = (final_count == initial_count + 5);
    print_test_result("保存计数器", test4_passed);
    
    printf("\n");
}

void print_test_result(const char* test_name, int passed)
{
    printf(">>> %s: %s <<<\n", test_name, passed ? "通过" : "失败");
}

// 模拟系统重启后的航向角恢复测试
void test_system_restart_simulation(void)
{
    printf("测试5: 模拟系统重启后航向角恢复\n");
    
    // 保存一个特定的航向角
    double pre_restart_azimuth = M_PI * 3.0 / 4.0;  // 135度
    printf("重启前保存航向角: %.2f 度\n", pre_restart_azimuth * 180.0 / M_PI);
    SaveAzimuthToFlash(pre_restart_azimuth);
    
    // 模拟系统重启 - 重新初始化
    InitAzimuthSaveSystem();
    
    // 检查是否能正确恢复
    if (IsAzimuthSaveValid()) {
        double post_restart_azimuth = RestoreAzimuthFromFlash();
        printf("重启后恢复航向角: %.2f 度\n", post_restart_azimuth * 180.0 / M_PI);
        
        double diff = fabs(pre_restart_azimuth - post_restart_azimuth);
        int test5_passed = (diff < 1e-6);
        print_test_result("系统重启恢复", test5_passed);
    } else {
        printf("重启后航向角数据无效!\n");
        print_test_result("系统重启恢复", 0);
    }
    
    printf("\n");
}

// 主测试入口
void run_azimuth_save_tests(void)
{
    printf("开始航向角掉电保存功能测试...\n\n");
    
    test_azimuth_save_restore();
    test_azimuth_validation();
    test_azimuth_multiple_saves();
    test_azimuth_counter();
    test_system_restart_simulation();
    
    printf("所有测试完成!\n");
}

// 调试信息打印函数
void print_azimuth_debug_info(void)
{
    printf("=== 航向角保存系统调试信息 ===\n");
    printf("保存的航向角: %.6f 弧度 (%.2f 度)\n", 
           stSetPara.SavedAzimuth, 
           stSetPara.SavedAzimuth * 180.0 / M_PI);
    printf("有效标志: 0x%08lX %s\n", 
           (unsigned long)stSetPara.AzimuthValidFlag,
           (stSetPara.AzimuthValidFlag == 0xA5A5A5A5) ? "(有效)" : "(无效)");
    printf("保存次数: %lu\n", (unsigned long)stSetPara.AzimuthSaveCount);
    printf("当前导航航向角: %.6f 弧度 (%.2f 度)\n", 
           NAV_Data_Full.SINS.att[2], 
           NAV_Data_Full.SINS.att[2] * 180.0 / M_PI);
    printf("===============================\n\n");
}
